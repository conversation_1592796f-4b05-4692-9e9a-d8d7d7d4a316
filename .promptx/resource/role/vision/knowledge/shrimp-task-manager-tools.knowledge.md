<knowledge>
  ## shrimp-task-manager工具清单（强制使用）
  
  ### 任务规划与分析工具
  - **plan_task**：任务规划指导，禁止假设猜测，必须收集信息
  - **analyze_task**：深入分析需求，评估技术可行性，使用pseudocode格式
  - **reflect_task**：批判性审查分析结果，识别优化机会
  - **split_tasks**：复杂任务分解，建立依赖关系（1-2工作天粒度，最多10项子任务）
  
  ### 任务管理与执行工具
  - **list_tasks**：生成结构化任务清单，包含状态追踪和优先级
  - **execute_task**：获取任务指导（注意：工具提供指导，需按步骤执行）
  - **verify_task**：任务验证评分，80分以上自动完成，低于80分提供修正建议
  - **update_task**：更新任务内容，已完成任务仅可更新摘要和相关文件
  
  ### 任务维护与查询工具
  - **delete_task**：删除未完成任务，保护已完成任务完整性
  - **clear_all_tasks**：清除未完成任务，重置任务列表
  - **query_task**：根据关键字或ID搜索任务
  - **get_task_detail**：获取任务完整详细信息
  
  ### 思考与研究工具
  - **process_thought**：灵活思考流程，建立质疑验证修正想法
  - **init_project_rules**：初始化或更新项目规范文件
  - **research_mode**：程式编程深度研究模式
  
  ## 工具使用原则
  - **强制专用**：所有任务管理必须且仅能使用shrimp-task-manager工具
  - **禁用内置**：严禁使用add_tasks、update_tasks、view_tasklist、reorganize_tasklist等内置工具
  - **工具验证**：每次任务管理操作前必须确认使用正确的shrimp工具
  - **违规零容忍**：发现使用内置任务工具立即停止并纠正
</knowledge>
